"use client";

import * as React from "react";
import { Button } from "@workspace/ui/components/button";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@workspace/ui/components/dialog";
import { Separator } from "@workspace/ui/components/separator";
import { LifeBuoy } from "lucide-react";
// @ts-ignore
import Image from "next/image";

export interface SummaryItem {
  name: string;
  sodaAmount: string;
}

export interface ServiceModifier {
  name: string;
  sodaAmount: string;
}

export interface OrderConfirmationDialogProps {
  // Dialog state
  open?: boolean;
  onOpenChange?: (open: boolean) => void;

  // Text content props with defaults
  title?: string;
  username?: string;
  unitCount?: number;
  unitType?: string;
  activityTitle?: string;

  // Data props
  avatar?: {
    src: string;
    alt?: string;
  };
  activity?: {
    src: string;
    alt?: string;
  };

  // Summary data
  mainItem: SummaryItem;
  serviceModifiers?: ServiceModifier[];
  totalSodaAmount?: number;

  // Button text
  submitButtonText?: string;
  supportButtonAriaLabel?: string;

  // Event handlers
  onSubmit?: () => void;
  onSupportClick?: () => void;
}

export function OrderConfirmationDialog({
  open = true,
  onOpenChange,
  mainItem,
  title = "Siparişini Onayla",
  username = "Haku-chan",
  unitCount = 3,
  unitType = "saatlik",
  activityTitle = "Valorant",
  avatar = {
    src: "",
    alt: "avatar",
  },
  activity = {
    src: "",
    alt: "activity",
  },
  serviceModifiers = [],
  totalSodaAmount = 0,
  submitButtonText = "GÖNDER",
  supportButtonAriaLabel = "SÜRECİ GÖSTER",
  onSubmit,
  onSupportClick,
}: OrderConfirmationDialogProps) {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <form>
        <DialogContent className="sm:max-w-[512px]">
          <DialogHeader>
            <DialogTitle>{title}</DialogTitle>
            <DialogDescription className="flex gap-5">
              <span className="flex shrink-0 items-start relative">
                <span className="border-3 rounded-sm overflow-hidden border-background ring-foreground ring-2">
                  <Image
                    src={avatar.src}
                    alt={avatar.alt || "avatar"}
                    width={90}
                    height={90}
                  />
                </span>
              </span>
              <span className="flex flex-col text-left justify-between gap-2 pr-1">
                <span id="description-text" className="pt-2.5 pr-1">
                  {username}'dan {unitCount}{" "}
                  {/^\d/.test(unitType) ? `adet ${unitType}` : unitType} sipariş
                  vermek üzeresin.
                </span>
                <span
                  id="service"
                  className="grid grid-cols-[1fr_auto] w-6/7 place-self-end items-center gap-2.5 text-foreground"
                >
                  <span className="grid grid-cols-[1fr_auto] gap-1">
                    <span
                      {...(activityTitle.length > 20 && {
                        title: activityTitle,
                      })}
                      id="service-title"
                      className="text-right truncate"
                    >
                      {activityTitle}
                    </span>
                    <span id="service-count">x{unitCount}</span>
                  </span>
                  <Image
                    src={activity.src}
                    alt={activity.alt || "activity"}
                    width={30}
                    height={30}
                    className="rounded-sm border-2 border-background ring-foreground ring-2"
                  />
                </span>
              </span>
            </DialogDescription>
          </DialogHeader>

          <section
            id="summary"
            className="[--ring:var(--background)] fake-stroke rounded-lg bg-muted text-sm text-muted-foreground mx-7 py-5 pl-5"
          >
            <div className="overflow-y-auto max-h-44 space-y-3 pr-5 scrollbar">
              <ul id="main-item">
                <li className="grid grid-cols-[1fr_auto] items-center gap-4">
                  <span
                    {...(mainItem.name.length > 30 && {
                      title: mainItem.name,
                    })}
                    className="truncate"
                  >
                    {mainItem.name}
                  </span>
                  <span className="shrink-0">
                    {mainItem.sodaAmount} soda x{unitCount}
                  </span>
                </li>
              </ul>
              {serviceModifiers && serviceModifiers.length > 0 && (
                <>
                  <Separator />
                  <ul id="service-modifiers" className="space-y-2 w-full">
                    {serviceModifiers.map((item, index) => (
                      <li
                        key={index}
                        className="grid grid-cols-[1fr_auto] items-center gap-4 w-full"
                      >
                        <span
                          {...(item.name.length > 30 && {
                            title: item.name,
                          })}
                          className="flex-1 min-w-0 truncate"
                        >
                          {item.name}
                        </span>
                        <span className="shrink-0">
                          {item.sodaAmount} soda x{unitCount}
                        </span>
                      </li>
                    ))}
                  </ul>
                </>
              )}
            </div>
            <div className="space-y-2 pr-5">
              <Separator />
              <ul id="total-soda" className="text-foreground text-lg">
                <li className="flex justify-between">
                  <span>{"Toplam"}</span>
                  <span>{totalSodaAmount + " soda"}</span>
                </li>
              </ul>
            </div>
          </section>

          <DialogFooter>
            <Button
              size={"default"}
              className="size-14"
              onClick={onSupportClick}
            >
              <LifeBuoy className="size-9" />
              <p className="sr-only">{supportButtonAriaLabel}</p>
            </Button>
            <DialogClose asChild>
              <Button variant={"primary"} onClick={onSubmit}>
                {submitButtonText}
              </Button>
            </DialogClose>
          </DialogFooter>
        </DialogContent>
      </form>
    </Dialog>
  );
}
